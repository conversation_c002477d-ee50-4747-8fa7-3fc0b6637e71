# Protect Bot Go Backend

This is the Go backend service for the Protect Bot Discord application. It handles database operations and provides an API for the Dart frontend to interact with.

## Prerequisites

- Go 1.20 or higher
- PostgreSQL database

## Setup

1. Install Go dependencies:

```bash
cd backend
go mod download
```

2. Configure environment variables:

Create a `.env` file in the `lib` directory with the following variables:

```
DISCORD_BOT_TOKEN=your_discord_bot_token
DB_HOST=localhost
DB_PORT=5432
DB_NAME=protect_bot
DB_USER=protect_bot_user
DB_PASSWORD=StrawberryYogurt
GO_BACKEND_HOST=localhost
GO_BACKEND_PORT=8080
```

3. Start the backend:

```bash
go run main.go
```

The backend will start on port 8080 by default.

## API Endpoints

- `GET /api/users` - Get all banned users
- `POST /api/users` - Add a new banned user
- `DELETE /api/users/{id}` - Remove a banned user
- `POST /api/users/check` - Check for matching users in a guild

## Database Schema

The backend uses a PostgreSQL database with the following schema:

```sql
CREATE TABLE IF NOT EXISTS logged_users (
  discord_user_id VARCHAR(255) PRIMARY KEY,
  discord_username VARCHAR(255) NOT NULL,
  banned_at TIMESTAMP NOT NULL,
  CONSTRAINT logged_users_user_id_key UNIQUE (discord_user_id)
);
```

## Integration with Dart Frontend

The Dart frontend communicates with this Go backend via HTTP requests. The `GoBackendService` class in the Dart code handles these requests.

## Running in Production

For production deployment, consider:

1. Using a process manager like systemd or PM2 to keep the backend running
2. Setting up a reverse proxy with Nginx or Caddy
3. Implementing proper authentication for the API endpoints
4. Using HTTPS for secure communication

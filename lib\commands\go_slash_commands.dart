import 'package:nyxx/nyxx.dart';
import 'package:nyxx_commands/nyxx_commands.dart';
import '../services/go_backend_service.dart';
import '../services/log_service.dart';

/// Handles registration and execution of slash commands for the bot using the Go backend
class GoSlashCommandHandler {
  final LogService _logService = LogService();
  final GoBackendService _backendService = GoBackendService();
  late final CommandsPlugin _commands;

  GoSlashCommandHandler() {
    _commands = CommandsPlugin(
      prefix: null, // No prefix for slash commands
      options: CommandsOptions(
        type: CommandType.slashOnly,
        defaultResponseLevel: ResponseLevel.public,
        logErrors: true,
      ),
      // Make sure slash commands are registered globally
      guild: null, // null means global registration
    );

    _registerCommands();
  }

  CommandsPlugin createCommandsPlugin() {
    return _commands;
  }

  /// Register all slash commands with Discord
  void _registerCommands() {
    // Help command
    _commands.addCommand(ChatCommand(
      'help',
      'Show available commands',
      id('help', (ChatContext context) async {
        // Respond directly without using our custom context
        await context.respond(MessageBuilder(
          content: '''
**Available Commands:**
`/help` - Show this help message
`/adduser <username> <user_id>` - Add a user to the watchlist
`/removeuser <user_id>` - Remove a user from the watchlist
`/ban <username> <user_id> [reason]` - Ban a user
`/unban <user_id>` - Unban a user
`/list` - List all banned users
`/scanguild` - Scan the current guild for banned users

**Note:** Some commands require moderator permissions.
'''
        ));
      }),
    ));

    // Add user command
    _commands.addCommand(ChatCommand(
      'adduser',
      'Add a user to the watchlist',
      id('adduser', (ChatContext context, String username, String user_id) async {
        try {
          await _backendService.addBannedUser(BannedUserData(
            discordUserId: user_id,
            discordUsername: username,
            bannedAt: DateTime.now(),
          ));

          await context.respond(MessageBuilder(
            content: '✅ User $username (ID: $user_id) added successfully',
          ));
          _logService.log('User $username (ID: $user_id) added by ${context.user.username}');
        } catch (e) {
          _logService.error('Failed to add user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to add user: $e',
          ));
        }
      }),
    ));

    // Ban user command
    _commands.addCommand(ChatCommand(
      'ban',
      'Ban a user',
      id('ban', (ChatContext context, String username, String user_id, [String? reason]) async {
        final banReason = reason ?? 'No reason provided';

        try {
          await _backendService.addBannedUser(BannedUserData(
            discordUserId: user_id,
            discordUsername: username,
            bannedAt: DateTime.now(),
          ));

          await context.respond(MessageBuilder(
            content: '✅ User $username (ID: $user_id) has been banned. Reason: $banReason',
          ));
          _logService.log('User $username (ID: $user_id) banned by ${context.user.username}. Reason: $banReason');
        } catch (e) {
          _logService.error('Failed to ban user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to ban user: $e',
          ));
        }
      }),
    ));

    // Unban user command
    _commands.addCommand(ChatCommand(
      'unban',
      'Unban a user',
      id('unban', (ChatContext context, String user_id) async {
        try {
          await _backendService.removeBannedUser(user_id);
          await context.respond(MessageBuilder(
            content: '✅ User with ID $user_id has been unbanned',
          ));
          _logService.log('User with ID $user_id unbanned by ${context.user.username}');
        } catch (e) {
          _logService.error('Failed to unban user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to unban user: $e',
          ));
        }
      }),
    ));

    // List banned users command
    _commands.addCommand(ChatCommand(
      'list',
      'List all banned users',
      id('list', (ChatContext context) async {
        try {
          final users = await _backendService.getAllUsers();
          if (users.isEmpty) {
            await context.respond(MessageBuilder(
              content: 'No banned users found.',
            ));
            return;
          }

          final userList = users.map((user) {
            String displayInfo;

            if (user.discordUsername.isNotEmpty && user.discordUserId.isNotEmpty) {
              // Both username and ID are available
              displayInfo = '${user.discordUsername} (ID: ${user.discordUserId})';
            } else if (user.discordUsername.isNotEmpty) {
              // Only username is available
              displayInfo = user.discordUsername;
            } else if (user.discordUserId.isNotEmpty) {
              // Only ID is available
              displayInfo = 'ID: ${user.discordUserId}';
            } else {
              // Neither is available (shouldn't happen, but just in case)
              displayInfo = 'Unknown user';
            }

            return '- $displayInfo';
          }).join('\n');

          await context.respond(MessageBuilder(
            content: '**Banned Users:**\n$userList',
          ));
        } catch (e) {
          _logService.error('Failed to list users', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to list users: $e',
          ));
        }
      }),
    ));

    // Remove user command
    _commands.addCommand(ChatCommand(
      'removeuser',
      'Remove a user from the watchlist',
      id('removeuser', (ChatContext context, String user_id) async {
        try {
          await _backendService.removeBannedUser(user_id);
          await context.respond(MessageBuilder(
            content: '✅ User with ID $user_id has been removed from the watchlist',
          ));
          _logService.log('User with ID $user_id removed from watchlist by ${context.user.username}');
        } catch (e) {
          _logService.error('Failed to remove user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to remove user: $e',
          ));
        }
      }),
    ));

    // Scan guild command
    _commands.addCommand(ChatCommand(
      'scanguild',
      'Scan the current guild for banned users',
      id('scanguild', (ChatContext context) async {
        try {
          // Get the guild from context
          final guild = context.guild;
          if (guild == null) {
            await context.respond(MessageBuilder(
              content: '❌ This command can only be used in a guild.',
            ));
            return;
          }

          await context.respond(MessageBuilder(
            content: '🔍 Scanning guild for logged users...',
          ));

          // Get all guild members using fetchMany with a high limit
          final members = guild.members;
          final memberList = await members.list(limit: 1000);

          _logService.log('Found ${memberList.length} total members in guild');

          // Convert ALL members (including bots) to GuildMember objects
          final guildMembers = memberList
              .where((member) => member.user != null)
              .map((member) => GuildMember(
                    id: member.user!.id.toString(),
                    username: member.user!.username,
                  ))
              .toList();

          _logService.log('Sending ${guildMembers.length} members to backend for checking');
          for (int i = 0; i < guildMembers.length && i < 10; i++) {
            _logService.log('Member ${i}: ID=${guildMembers[i].id}, Username=${guildMembers[i].username}');
          }

          // Check for matches using the Go backend
          final matchingUsers = await _backendService.checkUsers(guildMembers);

          if (matchingUsers.isEmpty) {
            await context.respond(MessageBuilder(
              content: '✅ No matching logged users found in this guild.',
            ));
            return;
          }

          // Find the corresponding Discord members for the matching users
          final matchingMembers = <Member>[];
          final embeds = <EmbedBuilder>[];
          final individualBanButtons = <ButtonBuilder>[];

          for (final bannedUser in matchingUsers) {
            for (final member in memberList) {
              if (member.user != null) {
                final memberId = member.user!.id.toString();
                final normalizedMemberId = memberId.replaceAll(RegExp(r'[^\d]'), '');
                final normalizedBannedId = bannedUser.discordUserId.replaceAll(RegExp(r'[^\d]'), '');

                if (normalizedMemberId == normalizedBannedId) {
                  matchingMembers.add(member);

                  // Build detailed user info
                  final user = member.user!;
                  final joinedAt = member.joinedAt.toLocal().toString().split('.')[0];
                  final accountCreated = user.id.timestamp.toLocal().toString().split('.')[0];

                  // Get role names with colors horizontally from left to right
                  final roleList = <String>[];
                  for (final roleRef in member.roles) {
                    try {
                      final fullRole = await roleRef.get();
                      roleList.add('🔸 ${fullRole.name}');
                    } catch (e) {
                      roleList.add('🔸 Unknown Role');
                    }
                  }
                  final rolesDisplay = roleList.isEmpty ? 'No roles' : roleList.join(' • ');

                  // Create embed with thumbnail on the left side
                  final embed = EmbedBuilder(
                    title: user.username,
                    description: '''User ID: ${user.id}
Account Created: ${accountCreated}
Server Joined Date: ${joinedAt}
Roles: ${rolesDisplay}''',
                    thumbnail: EmbedThumbnailBuilder(url: user.avatar.url),
                  );

                  embeds.add(embed);

                  // Create individual ban button for this user (limit to avoid Discord limits)
                  if (individualBanButtons.length < 20) { // Discord allows max 5 rows * 5 buttons - 1 for ban all = 24, but let's be safe
                    final individualBanButton = ButtonBuilder.danger(
                      customId: 'ban_user_${user.id}',
                      label: 'Ban ${user.username}',
                    );

                    individualBanButtons.add(individualBanButton);
                  }
                  break;
                }
              }
            }
          }

          // Create a button to ban all users
          final banAllButton = ButtonBuilder.danger(
            customId: 'ban_all_users_${context.guild!.id}',
            label: 'Ban All Users',
          );

          final banAllActionRow = ActionRowBuilder(components: [banAllButton]);

          // Combine all action rows (ban all button + individual ban buttons)
          final allComponents = <ActionRowBuilder>[banAllActionRow];
          allComponents.addAll(individualBanButtons);

          // Send the message with embeds and ban buttons
          final message = await context.respond(MessageBuilder(
            content: '''⚠️ **Found ${matchingMembers.length} matching logged users:**

You can ban all users at once or select individual users to ban.''',
            embeds: embeds,
            components: allComponents,
          ));

          // Wait for button press
          try {
            final buttonContext = await context.getButtonPress(message);
            final componentId = buttonContext.componentId;

            if (componentId == 'ban_all_users_${context.guild!.id}') {
              // Ban all users
              int bannedCount = 0;
              final errors = <String>[];

              for (final member in matchingMembers) {
                if (member.user != null) {
                  try {
                    await guild.createBan(member.user!.id, deleteMessages: Duration(days: 1));
                    bannedCount++;
                    _logService.log('Banned user ${member.user!.username} (${member.user!.id}) from guild ${guild.name}');
                  } catch (e) {
                    errors.add('Failed to ban ${member.user!.username}: $e');
                    _logService.error('Failed to ban user ${member.user!.username}', e);
                  }
                }
              }

              String response = '✅ Successfully banned $bannedCount users from the guild.';
              if (errors.isNotEmpty) {
                response += '\n\n❌ Errors:\n${errors.join('\n')}';
              }

              await buttonContext.respond(MessageBuilder(content: response));
            } else if (componentId.startsWith('ban_user_')) {
              // Ban individual user
              final userId = Snowflake.parse(componentId.substring(9));

              // Find the member to ban
              Member? memberToBan;
              for (final member in matchingMembers) {
                if (member.user?.id == userId) {
                  memberToBan = member;
                  break;
                }
              }

              if (memberToBan?.user != null) {
                try {
                  await guild.createBan(memberToBan!.user!.id, deleteMessages: Duration(days: 1));
                  _logService.log('Banned user ${memberToBan.user!.username} (${memberToBan.user!.id}) from guild ${guild.name}');

                  await buttonContext.respond(MessageBuilder(
                    content: '✅ Successfully banned ${memberToBan.user!.username} from the guild.',
                  ));
                } catch (e) {
                  _logService.error('Failed to ban user ${memberToBan?.user?.username}', e);
                  await buttonContext.respond(MessageBuilder(
                    content: '❌ Failed to ban ${memberToBan?.user?.username}: $e',
                  ));
                }
              } else {
                await buttonContext.respond(MessageBuilder(
                  content: '❌ User not found or no longer in the server.',
                ));
              }
            }
          } catch (e) {
            _logService.error('Error handling button press', e);
          }
        } catch (e) {
          _logService.error('Failed to scan guild', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to scan guild: $e',
          ));
        }
      }),
    ));
  }
}






















import 'dart:async';
import 'package:logging/logging.dart';

class LogService {
  final Logger _logger = Logger('ProtectBot');
  final StreamController<String> _logStreamController = StreamController<String>.broadcast();

  LogService() {
    // Configure logging
    Logger.root.level = Level.ALL;
    Logger.root.onRecord.listen((record) {
      final message = '${record.time}: ${record.level.name}: ${record.message}';
      _logStreamController.add(message);
      print(message); // Also print to console
    });
  }

  void log(String message) {
    _logger.info(message);
  }

  void error(String message, [Object? error, StackTrace? stackTrace]) {
    _logger.severe(message, error, stackTrace);
  }

  void warning(String message) {
    _logger.warning(message);
  }

  void debug(String message) {
    _logger.fine(message);
  }

  Stream<String> get logStream => _logStreamController.stream;

  void dispose() {
    _logStreamController.close();
  }
}

import 'package:nyxx/nyxx.dart';
import '../services/database_service.dart';
import '../services/log_service.dart';

class CommandHandler {
  final LogService _logService = LogService();
  final Map<String, Function> _commands = {};

  CommandHandler() {
    _registerCommands();
  }

  void _registerCommands() {
    _commands['help'] = _helpCommand;
    _commands['adduser'] = _addUserCommand;
    _commands['ban'] = _banCommand;
    _commands['unban'] = _unbanCommand;
    _commands['list'] = _listCommand;
    _commands['removeuser'] = _removeUserCommand;
  }

  Future<void> handleCommand(
    String command,
    MessageCreateEvent event,
    DatabaseService databaseService,
  ) async {
    final handler = _commands[command];
    if (handler == null) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: 'Unknown command. Use /help to see available commands.',
      ));
      return;
    }

    try {
      await handler(event, databaseService);
    } catch (e, stackTrace) {
      _logService.error('Error executing command $command', e, stackTrace);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ An error occurred while executing the command. Please try again later.',
      ));
    }
  }

  Future<void> _helpCommand(MessageCreateEvent event, DatabaseService databaseService) async {
    final helpMessage = '''
**Available Commands:**
`/help` - Show this help message
`/adduser <username> <user_id>` - Add a user to the watchlist
`/removeuser <user_id>` - Remove a user from the watchlist
`/ban <username> <user_id> [reason]` - Ban a user
`/unban <user_id>` - Unban a user
`/list` - List all banned users

**Note:** Some commands require moderator permissions.
''';

    await event.message.channel.sendMessage(MessageBuilder(
      content: helpMessage,
    ));
  }

  Future<void> _addUserCommand(MessageCreateEvent event, DatabaseService databaseService) async {
    final parts = event.message.content.split(' ');
    if (parts.length < 3) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Usage: /adduser <username> <user_id>',
      ));
      return;
    }

    final username = parts[1].trim();
    final userId = parts[2].trim();

    if (username.isEmpty) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Username cannot be empty',
      ));
      return;
    }

    if (userId.isEmpty) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ User ID cannot be empty',
      ));
      return;
    }

    try {
      await databaseService.addBannedUser(BannedUserData(
        discordUserId: userId,
        discordUsername: username,
        bannedAt: DateTime.now(),
      ));

      await event.message.channel.sendMessage(MessageBuilder(
        content: '✅ User $username (ID: $userId) added successfully',
      ));
      _logService.log('User $username (ID: $userId) added by ${event.message.author.username}');
    } catch (e) {
      _logService.error('Failed to add user', e);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Failed to add user: $e',
      ));
    }
  }

  Future<void> _banCommand(MessageCreateEvent event, DatabaseService databaseService) async {
    final parts = event.message.content.split(' ');
    if (parts.length < 3) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Usage: /ban <username> <user_id> [reason]',
      ));
      return;
    }

    final username = parts[1].trim();
    final userId = parts[2].trim();

    if (username.isEmpty) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Username cannot be empty',
      ));
      return;
    }

    if (userId.isEmpty) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ User ID cannot be empty',
      ));
      return;
    }

    final reason = parts.length > 3 ? parts.sublist(3).join(' ').trim() : 'No reason provided';

    try {
      await databaseService.addBannedUser(BannedUserData(
        discordUserId: userId,
        discordUsername: username,
        bannedAt: DateTime.now(),
      ));

      await event.message.channel.sendMessage(MessageBuilder(
        content: '✅ User $username (ID: $userId) has been banned. Reason: $reason',
      ));
      _logService.log('User $username (ID: $userId) banned by ${event.message.author.username}. Reason: $reason');
    } catch (e) {
      _logService.error('Failed to ban user', e);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Failed to ban user: $e',
      ));
    }
  }

  Future<void> _unbanCommand(MessageCreateEvent event, DatabaseService databaseService) async {
    final parts = event.message.content.split(' ');
    if (parts.length < 2) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Usage: /unban <user_id>',
      ));
      return;
    }

    final userId = parts[1].trim();
    if (userId.isEmpty) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ User ID cannot be empty',
      ));
      return;
    }

    try {
      await databaseService.removeBannedUser(userId);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '✅ User with ID $userId has been unbanned',
      ));
      _logService.log('User with ID $userId unbanned by ${event.message.author.username}');
    } catch (e) {
      _logService.error('Failed to unban user', e);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Failed to unban user: $e',
      ));
    }
  }

  Future<void> _listCommand(MessageCreateEvent event, DatabaseService databaseService) async {
    try {
      final users = await databaseService.getAllUsers();
      if (users.isEmpty) {
        await event.message.channel.sendMessage(MessageBuilder(
          content: 'No banned users found.',
        ));
        return;
      }

      final userList = users.map((user) {
        String displayInfo;

        if (user.discordUsername.isNotEmpty && user.discordUserId.isNotEmpty) {
          // Both username and ID are available
          displayInfo = '${user.discordUsername} (ID: ${user.discordUserId})';
        } else if (user.discordUsername.isNotEmpty) {
          // Only username is available
          displayInfo = user.discordUsername;
        } else if (user.discordUserId.isNotEmpty) {
          // Only ID is available
          displayInfo = 'ID: ${user.discordUserId}';
        } else {
          // Neither is available (shouldn't happen, but just in case)
          displayInfo = 'Unknown user';
        }

        return '- $displayInfo';
      }).join('\n');

      await event.message.channel.sendMessage(MessageBuilder(
        content: '**Banned Users:**\n$userList',
      ));
    } catch (e) {
      _logService.error('Failed to list users', e);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Failed to list users: $e',
      ));
    }
  }

  Future<void> _removeUserCommand(MessageCreateEvent event, DatabaseService databaseService) async {
    final parts = event.message.content.split(' ');
    if (parts.length < 2) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Usage: /removeuser <user_id>',
      ));
      return;
    }

    final userId = parts[1].trim();
    if (userId.isEmpty) {
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ User ID cannot be empty',
      ));
      return;
    }

    try {
      await databaseService.removeBannedUser(userId);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '✅ User with ID $userId has been removed from the watchlist',
      ));
      _logService.log('User with ID $userId removed from watchlist by ${event.message.author.username}');
    } catch (e) {
      _logService.error('Failed to remove user', e);
      await event.message.channel.sendMessage(MessageBuilder(
        content: '❌ Failed to remove user: $e',
      ));
    }
  }
}

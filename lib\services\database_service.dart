import 'package:postgres/postgres.dart';
import 'package:dotenv/dotenv.dart';
import 'log_service.dart';

class BannedUserData {
  final String discordUserId;
  final String discordUsername;
  final DateTime bannedAt;

  BannedUserData({required this.discordUserId, required this.discordUsername, required this.bannedAt});

  factory BannedUserData.fromMap(Map<String, dynamic> map) {
    return BannedUserData(
      discordUserId: map['discord_user_id'] as String,
      discordUsername: map['discord_username'] as String,
      bannedAt: map['banned_at'] is DateTime
          ? map['banned_at']
          : DateTime.parse(map['banned_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'discord_user_id': discordUserId,
      'discord_username': discordUsername,
      'banned_at': bannedAt.toIso8601String(),
    };
  }
}

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;

  final LogService _logService = LogService();
  PostgreSQLConnection? _connection;
  bool _initialized = false;

  DatabaseService._internal();

  Future<void> initialize() async {
    if (_initialized) return;

    try {
  final env = DotEnv(includePlatformEnvironment: true)..load(['C:/Users/<USER>/protect_bot/.env']);

      final host = env['DB_HOST'] ?? 'localhost';
      final port = int.parse(env['DB_PORT'] ?? '5432');
      final database = env['DB_NAME'] ?? 'protect_bot';
      final username = env['DB_USER'] ?? 'protect_bot_user';
      final password = env['DB_PASSWORD'] ?? 'StrawberryYogurt';

      _logService.log('Attempting to connect to PostgreSQL database at $host:$port');

      _connection = PostgreSQLConnection(
        host,
        port,
        database,
        username: username,
        password: password,
        timeoutInSeconds: 5,
      );

      await _connection!.open();
      await _createTables();
      _logService.log('Connected to PostgreSQL database');

      _initialized = true;
    } catch (e) {
      _logService.log('Error connecting to database: $e');
      rethrow;
    }
  }

  Future<void> _createTables() async {
    await _connection!.execute('''
      CREATE TABLE IF NOT EXISTS logged_users (
        discord_user_id VARCHAR(255) PRIMARY KEY,
        discord_username VARCHAR(255) NOT NULL,
        banned_at TIMESTAMP NOT NULL,
        CONSTRAINT logged_users_user_id_key UNIQUE (discord_user_id)
      );
    ''');
  }

  Future<void> addBannedUser(BannedUserData user) async {
    if (_connection == null) {
      throw Exception('Database not initialized');
    }

    try {
      await _connection!.execute(
        '''
        INSERT INTO logged_users (
          discord_user_id, discord_username, banned_at
        ) VALUES (
          @userId, @username, @bannedAt
        )
        ON CONFLICT (discord_user_id) DO UPDATE SET
          discord_username = @username,
          banned_at = @bannedAt
        ''' ,
        substitutionValues: {
          'userId': user.discordUserId,
          'username': user.discordUsername,
          'bannedAt': user.bannedAt,
        },
      );
    } catch (e) {
      _logService.log('Failed to add banned user: $e');
      rethrow;
    }
  }

  Future<void> removeBannedUser(String userId) async {
    if (_connection == null) {
      throw Exception('Database not initialized');
    }

    try {
      await _connection!.execute(
        'DELETE FROM logged_users WHERE discord_user_id = @userId',
        substitutionValues: {
          'userId': userId,
        },
      );
    } catch (e) {
      _logService.log('Failed to remove banned user: $e');
      rethrow;
    }
  }

  Future<List<BannedUserData>> getAllUsers() async {
    if (_connection == null) {
      throw Exception('Database not initialized');
    }

    try {
      final results = await _connection!.mappedResultsQuery(
        'SELECT * FROM logged_users ORDER BY banned_at DESC',
      );

      return results.map((row) => BannedUserData.fromMap(row['logged_users']!)).toList();
    } catch (e) {
      _logService.log('Failed to get all users: $e');
      rethrow;
    }
  }

  Future<void> close() async {
    await _connection?.close();
  }

  Future<bool> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
    return _initialized;
  }
}




# Protect Bot

A Discord bot for user protection and moderation.

## Features

- Add users to a watchlist
- Ban users with reason
- Unban users
- List all banned users
- Discord slash commands with auto-complete and parameter hints
- Permission-based command access
- Command cooldowns
- PostgreSQL database integration
- Logging system

## Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   dart pub get
   ```
3. Create a `.env` file in the `lib` directory with the following content:
   ```
   DISCORD_BOT_TOKEN=your_discord_bot_token_here
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=protect_bot
   DB_USER=protect_bot_user
   DB_PASSWORD=your_database_password
   ```
4. Set up PostgreSQL:
   - Create a database named `protect_bot`
   - Create a user named `protect_bot_user` with the password from your `.env` file
   - Grant necessary permissions to the user

## Running the Bot

```bash
dart run lib/main.dart
```

## Commands

The bot uses Discord's slash commands, which appear in the Discord UI when you type `/`:

- `/help` - Show available commands
- `/adduser` - Add a user to the watchlist (requires username and user ID)
- `/ban` - Ban a user (requires username, user ID, and optional reason)
- `/unban` - Unban a user (requires user ID)
- `/list` - List all banned users
- `/removeuser` - Remove a user from the watchlist (requires user ID)

## Permissions

- `/adduser`, `/ban`, and `/unban` commands require moderator permissions (ban/kick members)
- Other commands are available to everyone

## Development

This project uses:
- [nyxx](https://pub.dev/packages/nyxx) for Discord API integration
- [nyxx_commands](https://pub.dev/packages/nyxx_commands) for command handling
- [postgres](https://pub.dev/packages/postgres) for database operations
- [dotenv](https://pub.dev/packages/dotenv) for environment variable management
- [logging](https://pub.dev/packages/logging) for logging

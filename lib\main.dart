import 'dart:io';
import 'package:dotenv/dotenv.dart';
import 'discord_bot.dart';
import 'services/log_service.dart';
import 'services/go_backend_service.dart';

void main() async {
  // Initialize services
  final logService = LogService();
  final backendService = GoBackendService();
  final discordBot = DiscordBot();

  try {
    // Load environment variables from lib directory
  // Load .env from the project root using absolute path for Windows compatibility
  final env = DotEnv(includePlatformEnvironment: true)..load(['C:/Users/<USER>/protect_bot/.env']);
    final discordToken = env['DISCORD_BOT_TOKEN'];

    if (discordToken == null || discordToken.isEmpty) {
      logService.error('Discord bot token not found in environment variables');
      return;
    }

    // Start the Go backend process
    logService.log('Starting Go backend service...');
    await startGoBackend();
    logService.log('Go backend service started successfully');

    // Initialize Go backend service
    logService.log('Initializing Go backend service...');
    await backendService.initialize();
    logService.log('Go backend service initialized successfully');

    // Initialize Discord bot
    logService.log('Initializing Discord bot...');
    await discordBot.initialize(discordToken);
    logService.log('Discord bot initialized successfully');

    // Start monitoring
    final monitor = BotMonitor(logService, backendService);
    monitor.startMonitoring();

    // Keep the bot running
    logService.log('Bot is now running. Press Ctrl+C to stop.');
  } catch (e, stackTrace) {
    logService.error('Failed to start bot', e, stackTrace);
  }
}

// Start the Go backend process
Future<Process> startGoBackend() async {
  // Check if the Go backend is already running
  try {
    final result = await Process.run('curl', ['http://localhost:8080/api/users']);
    if (result.exitCode == 0) {
      print('Go backend is already running');
      throw Exception('Go backend is already running');
    }
  } catch (e) {
    // If curl fails, the backend is not running, which is what we want
    print('Go backend is not running, starting it now');
  }

  // Start the Go backend process
  final process = await Process.start(
    'go',
    ['run', 'backend/main.go'],
    workingDirectory: Directory.current.path,
    mode: ProcessStartMode.detached,
  );

  // Wait a moment for the backend to start
  await Future.delayed(Duration(seconds: 2));

  return process;
}

// Simple console-based UI for monitoring the bot
class BotMonitor {
  final LogService _logService;
  final GoBackendService _backendService;

  BotMonitor(this._logService, this._backendService);

  void startMonitoring() {
    _logService.logStream.listen((logMessage) {
      print(logMessage);
    });

    // Periodically check database status
    Future.delayed(Duration(seconds: 30), () async {
      try {
        final users = await _backendService.getAllUsers();
        print('\nCurrent banned users: ${users.length}');
        for (final user in users) {
          print('- ${user.discordUserId}');
        }
      } catch (e) {
        print('Error checking database status: $e');
      }
    });
  }
}

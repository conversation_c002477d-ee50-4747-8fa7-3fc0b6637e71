import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:dotenv/dotenv.dart';
import 'log_service.dart';

class BannedUserData {
  final String discordUserId;
  final String discordUsername;
  final DateTime bannedAt;

  BannedUserData({required this.discordUserId, required this.discordUsername, required this.bannedAt});

  factory BannedUserData.fromMap(Map<String, dynamic> map) {
    return BannedUserData(
      discordUserId: map['discord_user_id'] as String,
      discordUsername: map['discord_username'] as String,
      bannedAt: map['banned_at'] is DateTime
          ? map['banned_at']
          : DateTime.parse(map['banned_at'] as String),
    );
  }

  factory BannedUserData.fromJson(Map<String, dynamic> json) {
    return BannedUserData(
      discordUserId: json['discord_user_id'] as String,
      discordUsername: json['discord_username'] as String,
      bannedAt: DateTime.parse(json['banned_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'discord_user_id': discordUserId,
      'discord_username': discordUsername,
      'banned_at': bannedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toJson() {
    return {
      'discord_user_id': discordUserId,
      'discord_username': discordUsername,
      'banned_at': bannedAt.toIso8601String(),
    };
  }
}

class GuildMember {
  final String id;
  final String username;

  GuildMember({required this.id, required this.username});

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
    };
  }
}

class GoBackendService {
  static final GoBackendService _instance = GoBackendService._internal();
  factory GoBackendService() => _instance;

  final LogService _logService = LogService();
  late String _baseUrl;
  bool _initialized = false;

  GoBackendService._internal();

  Future<void> initialize() async {
    if (_initialized) return;

    try {
  final env = DotEnv(includePlatformEnvironment: true)..load(['C:/Users/<USER>/protect_bot/.env']);

  final host = env['GO_BACKEND_HOST'] ?? 'localhost';
  final port = env['GO_BACKEND_PORT'] ?? '8081';

  _baseUrl = 'http://$host:$port/api';
      _logService.log('Go backend service initialized with base URL: $_baseUrl');

      _initialized = true;
    } catch (e) {
      _logService.log('Error initializing Go backend service: $e');
      rethrow;
    }
  }

  Future<void> addBannedUser(BannedUserData user) async {
    await _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/users'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(user.toJson()),
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to add banned user: ${response.body}');
      }

      _logService.log('User ${user.discordUsername} (ID: ${user.discordUserId}) added successfully');
    } catch (e) {
      _logService.log('Failed to add banned user: $e');
      rethrow;
    }
  }

  Future<void> removeBannedUser(String userId) async {
    await _ensureInitialized();

    try {
      final response = await http.delete(
        Uri.parse('$_baseUrl/users/$userId'),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to remove banned user: ${response.body}');
      }

      _logService.log('User with ID $userId removed successfully');
    } catch (e) {
      _logService.log('Failed to remove banned user: $e');
      rethrow;
    }
  }

  Future<List<BannedUserData>> getAllUsers() async {
    await _ensureInitialized();

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/users'),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to get all users: ${response.body}');
      }

      final List<dynamic> jsonList = jsonDecode(response.body);
      return jsonList.map((json) => BannedUserData.fromJson(json)).toList();
    } catch (e) {
      _logService.log('Failed to get all users: $e');
      rethrow;
    }
  }

  Future<List<BannedUserData>> checkUsers(List<GuildMember> members) async {
    await _ensureInitialized();

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/users/check'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'guild_members': members.map((member) => member.toJson()).toList(),
        }),
      );

      if (response.statusCode != 200) {
        print('Error code: [31m${response.statusCode}[0m');
        print('Error message: [33m${response.body}[0m');
        print('Stacktrace:');
        try {
          throw Exception('Failed to check users: ${response.body}');
        } catch (e, stack) {
          print(stack);
          rethrow;
        }
      }

      final Map<String, dynamic> jsonResponse = jsonDecode(response.body);
      final List<dynamic> matchingUsers = jsonResponse['matching_users'];
      return matchingUsers.map((json) => BannedUserData.fromJson(json)).toList();
    } catch (e) {
      _logService.log('Failed to check users: $e');
      rethrow;
    }
  }

  Future<bool> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
    return _initialized;
  }
}

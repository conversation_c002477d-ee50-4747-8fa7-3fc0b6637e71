import 'package:nyxx/nyxx.dart';
import 'package:nyxx_commands/nyxx_commands.dart';
import '../services/database_service.dart';
import '../services/log_service.dart';

/// Handles registration and execution of slash commands for the bot
class SlashCommandHandler {
  final LogService _logService = LogService();
  final DatabaseService _databaseService;
  late final CommandsPlugin _commands;

  SlashCommandHandler(this._databaseService) {
    _commands = CommandsPlugin(
      prefix: null, // No prefix for slash commands - we only want slash commands
      options: CommandsOptions(
        type: CommandType.slashOnly,
        defaultResponseLevel: ResponseLevel.public,
        logErrors: true,
      ),
      // Make sure slash commands are registered globally
      guild: null, // null means global registration
    );
  }

  /// Create the commands plugin
  CommandsPlugin createCommandsPlugin() {
    _logService.log('Creating commands plugin...');
    return _commands;
  }

  /// Register all slash commands with Discord
  void registerCommands() {
    _registerCommands();
    _logService.log('Registering slash commands with Discord...');
  }

  /// Initialize and register all slash commands (legacy method)
  CommandsPlugin initialize([NyxxGateway? client]) {
    // The CommandsPlugin should be registered when creating the client
    // We'll just register our commands here
    _registerCommands();

    // Log that commands are being registered
    _logService.log('Registering slash commands with Discord...');

    return _commands;
  }

  /// Register all slash commands with Discord
  void _registerCommands() {
    // Help command
    _commands.addCommand(ChatCommand(
      'help',
      'Show available commands',
      id('help', (ChatContext context) async {
        // Respond directly without using our custom context
        await context.respond(MessageBuilder(
          content: '''
**Available Commands:**
`/help` - Show this help message
`/adduser <username> <user_id>` - Add a user to the watchlist
`/removeuser <user_id>` - Remove a user from the watchlist
`/ban <username> <user_id> [reason]` - Ban a user
`/unban <user_id>` - Unban a user
`/list` - List all banned users
`/scanguild` - Scan the current guild for banned users

**Note:** Some commands require moderator permissions.
'''
        ));
      }),
    ));

    // Add user command
    _commands.addCommand(ChatCommand(
      'adduser',
      'Add a user to the watchlist',
      id('adduser', (ChatContext context, String username, String user_id) async {
        try {
          await _databaseService.addBannedUser(BannedUserData(
            discordUserId: user_id,
            discordUsername: username,
            bannedAt: DateTime.now(),
          ));

          await context.respond(MessageBuilder(
            content: '✅ User $username (ID: $user_id) added successfully',
          ));
          _logService.log('User $username (ID: $user_id) added by ${context.user.username}');
        } catch (e) {
          _logService.error('Failed to add user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to add user: $e',
          ));
        }
      }),
    ));

    // Ban user command
    _commands.addCommand(ChatCommand(
      'ban',
      'Ban a user',
      id('ban', (ChatContext context, String username, String user_id, [String? reason]) async {
        final banReason = reason ?? 'No reason provided';

        try {
          await _databaseService.addBannedUser(BannedUserData(
            discordUserId: user_id,
            discordUsername: username,
            bannedAt: DateTime.now(),
          ));

          await context.respond(MessageBuilder(
            content: '✅ User $username (ID: $user_id) has been banned. Reason: $banReason',
          ));
          _logService.log('User $username (ID: $user_id) banned by ${context.user.username}. Reason: $banReason');
        } catch (e) {
          _logService.error('Failed to ban user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to ban user: $e',
          ));
        }
      }),
    ));

    // Unban user command
    _commands.addCommand(ChatCommand(
      'unban',
      'Unban a user',
      id('unban', (ChatContext context, String user_id) async {
        try {
          await _databaseService.removeBannedUser(user_id);
          await context.respond(MessageBuilder(
            content: '✅ User with ID $user_id has been unbanned',
          ));
          _logService.log('User with ID $user_id unbanned by ${context.user.username}');
        } catch (e) {
          _logService.error('Failed to unban user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to unban user: $e',
          ));
        }
      }),
    ));

    // List banned users command
    _commands.addCommand(ChatCommand(
      'list',
      'List all banned users',
      id('list', (ChatContext context) async {
        try {
          final users = await _databaseService.getAllUsers();
          if (users.isEmpty) {
            await context.respond(MessageBuilder(
              content: 'No banned users found.',
            ));
            return;
          }

          final userList = users.map((user) {
            String displayInfo;

            if (user.discordUsername.isNotEmpty && user.discordUserId.isNotEmpty) {
              // Both username and ID are available
              displayInfo = '${user.discordUsername} (ID: ${user.discordUserId})';
            } else if (user.discordUsername.isNotEmpty) {
              // Only username is available
              displayInfo = user.discordUsername;
            } else if (user.discordUserId.isNotEmpty) {
              // Only ID is available
              displayInfo = 'ID: ${user.discordUserId}';
            } else {
              // Neither is available (shouldn't happen, but just in case)
              displayInfo = 'Unknown user';
            }

            return '- $displayInfo';
          }).join('\n');

          await context.respond(MessageBuilder(
            content: '**Banned Users:**\n$userList',
          ));
        } catch (e) {
          _logService.error('Failed to list users', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to list users: $e',
          ));
        }
      }),
    ));

    // Remove user command
    _commands.addCommand(ChatCommand(
      'removeuser',
      'Remove a user from the watchlist',
      id('removeuser', (ChatContext context, String user_id) async {
        try {
          await _databaseService.removeBannedUser(user_id);
          await context.respond(MessageBuilder(
            content: '✅ User with ID $user_id has been removed from the watchlist',
          ));
          _logService.log('User with ID $user_id removed from watchlist by ${context.user.username}');
        } catch (e) {
          _logService.error('Failed to remove user', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to remove user: $e',
          ));
        }
      }),
    ));

    // Scan guild command
    _commands.addCommand(ChatCommand(
      'scanguild',
      'Scan the current guild for banned users',
      id('scanguild', (ChatContext context) async {
        try {
          // Get the guild from context
          final guild = context.guild;
          if (guild == null) {
            await context.respond(MessageBuilder(
              content: '❌ This command can only be used in a guild.',
            ));
            return;
          }

          await context.respond(MessageBuilder(
            content: '🔍 Scanning guild for banned users...',
          ));

          // Get all banned users from database
          final bannedUsers = await _databaseService.getAllUsers();
          if (bannedUsers.isEmpty) {
            await context.respond(MessageBuilder(
              content: 'No banned users found in the database.',
            ));
            return;
          }

          _logService.log('Found ${bannedUsers.length} banned users in database');

          // Get all guild members
          final members = await guild.members;
          final memberList = await members.list();
          final matchingMembers = <Member>[];

          _logService.log('Found ${memberList.length} members in guild');

          // Check each member against banned users
          for (final member in memberList) {
            if (member.user == null) continue;

            final memberId = member.user!.id.toString();
            final normalizedMemberId = memberId.replaceAll(RegExp(r'[^\d]'), '');

            for (final bannedUser in bannedUsers) {
              final bannedId = bannedUser.discordUserId;
              final normalizedBannedId = bannedId.replaceAll(RegExp(r'[^\d]'), '');

              if (normalizedMemberId == normalizedBannedId || memberId == bannedId) {
                _logService.log('Match found: ${member.user!.username} (${member.user!.id})');
                matchingMembers.add(member);
                break;
              }
            }
          }

          if (matchingMembers.isEmpty) {
            await context.respond(MessageBuilder(
              content: '✅ No matching banned users found in this guild.',
            ));
          } else {
            // Create a list of users found
            final userList = matchingMembers.map((member) =>
              '- ${member.user?.username} (${member.user?.id})'
            ).join('\n');

            // Create action row with buttons for ban options
            final actionRow = ActionRowBuilder(
              components: [
                ButtonBuilder(
                  label: 'Ban All',
                  style: ButtonStyle.danger,
                  customId: 'ban_all_users',
                ),
                ButtonBuilder(
                  label: 'Choose Specific Users',
                  style: ButtonStyle.primary,
                  customId: 'choose_specific_users',
                ),
              ],
            );

            // Send the message with buttons
            final message = await context.respond(MessageBuilder(
              content: '⚠️ **Found matching banned users:**\n\n$userList\n\nWould you like to ban these users?',
              components: [actionRow],
            ));

            // Wait for button press
            try {
              final buttonContext = await context.getButtonPress(message);

              if (buttonContext.componentId == 'ban_all_users') {
                // Ban all users
                int bannedCount = 0;

                for (final member in matchingMembers) {
                  if (member.user != null) {
                    try {
                      // Ban the user from the guild
                      await guild.createBan(member.user!.id);
                      bannedCount++;
                    } catch (e) {
                      _logService.error('Failed to ban user ${member.user?.username}', e);
                    }
                  }
                }

                await buttonContext.respond(MessageBuilder(
                  content: '✅ Successfully banned $bannedCount users from the guild.',
                ));
              } else if (buttonContext.componentId == 'choose_specific_users') {
                // Create individual ban buttons for each user
                final userButtons = <ActionRowBuilder>[];

                for (final member in matchingMembers) {
                  if (member.user != null) {
                    userButtons.add(ActionRowBuilder(
                      components: [
                        ButtonBuilder(
                          label: 'Ban ${member.user?.username}',
                          style: ButtonStyle.danger,
                          customId: 'ban_user_${member.user?.id}',
                        ),
                      ],
                    ));
                  }
                }

                // Send message with user-specific ban buttons
                final userSelectionMessage = await buttonContext.respond(MessageBuilder(
                  content: '**Select users to ban:**',
                  components: userButtons,
                ));

                // Wait for a button press on any of the user-specific buttons
                try {
                  final userButtonContext = await buttonContext.getButtonPress(userSelectionMessage);
                  final componentId = userButtonContext.componentId;

                  if (componentId.startsWith('ban_user_')) {
                    final userId = Snowflake.parse(componentId.substring(9));

                    try {
                      // Ban the user
                      await guild.createBan(userId);

                      // Find the username for the banned user
                      String? username;
                      for (final member in matchingMembers) {
                        if (member.user?.id == userId) {
                          username = member.user?.username;
                          break;
                        }
                      }

                      await userButtonContext.respond(MessageBuilder(
                        content: '✅ Successfully banned ${username ?? userId} from the guild.',
                      ));
                    } catch (e) {
                      _logService.error('Failed to ban user with ID $userId', e);
                      await userButtonContext.respond(MessageBuilder(
                        content: '❌ Failed to ban user: $e',
                      ));
                    }
                  }
                } catch (e) {
                  _logService.error('Error handling user selection', e);
                }
              }
            } catch (e) {
              _logService.error('Error handling button press', e);
            }
          }
        } catch (e) {
          _logService.error('Failed to scan guild', e);
          await context.respond(MessageBuilder(
            content: '❌ Failed to scan guild: $e',
          ));
        }
      }),
    ));
  }
}





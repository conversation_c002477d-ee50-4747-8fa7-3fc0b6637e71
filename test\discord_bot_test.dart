import 'package:test/test.dart';
import 'package:protect_bot/discord_bot.dart';
import 'package:protect_bot/services/database_service.dart';
import 'package:protect_bot/services/log_service.dart';

void main() {
  late DiscordBot discordBot;
  late DatabaseService databaseService;
  late LogService logService;

  setUp(() {
    logService = LogService();
    databaseService = DatabaseService();
    discordBot = DiscordBot();
  });

  test('Bot initialization with empty token should not initialize', () async {
    await discordBot.initialize('');
    expect(discordBot.isInitialized, false);
  });

  test('Database service should be a singleton', () {
    final db1 = DatabaseService();
    final db2 = DatabaseService();
    expect(identical(db1, db2), true);
  });

  test('Log service should be a singleton', () {
    final log1 = LogService();
    final log2 = LogService();
    expect(identical(log1, log2), true);
  });

  // Add more tests as needed for command handling, database operations, etc.
} 
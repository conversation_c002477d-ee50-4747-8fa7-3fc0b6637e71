import 'package:nyxx/nyxx.dart';
import 'package:nyxx_commands/nyxx_commands.dart';
import 'services/log_service.dart';
import 'services/go_backend_service.dart';
import 'commands/go_slash_commands.dart';

class DiscordBot {
  final LogService _logService = LogService();
  final GoBackendService _backendService = GoBackendService();
  late final GoSlashCommandHandler _slashCommandHandler;
  NyxxGateway? _bot;
  bool _isInitialized = false;
  String? _token;

  // Set to track guilds we've seen in this session to avoid duplicate messages
  final Set<String> _seenGuilds = {};

  Future<void> initialize(String token) async {
    if (token.isEmpty) {
      _logService.log('Discord token is empty, bot will not be initialized');
      return;
    }

    // Initialize the Go backend service
    await _backendService.initialize();

    _token = token;
    await _connect();
  }

  Future<void> _connect() async {
    if (_token == null) {
      _logService.error('Cannot connect: No token available');
      return;
    }

    try {
      // Initialize slash command handler
      _slash<PERSON><PERSON><PERSON>Handler = GoSlashCommandHandler();

      // Create the commands plugin with proper configuration
      final commandsPlugin = _slashCommandHandler.createCommandsPlugin();

      // Create bot instance with all necessary intents and the commands plugin
      _bot = await Nyxx.connectGateway(
        _token!,
        GatewayIntents.allUnprivileged | GatewayIntents.messageContent | GatewayIntents.guildMembers,
        options: GatewayClientOptions(
          plugins: [commandsPlugin],
        ),
      );

      // Commands are registered automatically in the GoSlashCommandHandler constructor

      // Set up event handlers
      _bot?.onReady.listen(_onReady);

      // Only process guild join events for newly joined guilds
      _bot?.onGuildCreate.listen((event) {
        final guildEvent = event as GuildCreateEvent;

        // Check if this is a newly joined guild by comparing timestamps
        // If we can't determine, we'll assume it's not new to avoid spam
        _logService.log('Guild event received: ${guildEvent.guild.name}');

        // For now, we'll only process the event if it's the first time seeing this guild
        // in this session (using a static set to track seen guilds)
        if (!_seenGuilds.contains(guildEvent.guild.id.toString())) {
          _seenGuilds.add(guildEvent.guild.id.toString());
          // Only process the first guild event after startup
          if (_seenGuilds.length == 1) {
            _onGuildJoin(guildEvent);
          }
        }
      });

      _logService.log('Discord bot connected successfully');
      _isInitialized = true;
    } catch (e, stackTrace) {
      _logService.error('Failed to connect Discord bot', e, stackTrace);
      _isInitialized = false;

      // Don't automatically reconnect - this can cause an infinite loop
      // Instead, log the error and let the caller decide what to do
      _logService.log('Bot connection failed. Please check your token and network connection.');
    }
  }

  void _onReady(ReadyEvent event) {
    _logService.log('Bot is ready!');
  }

  Future<void> _onGuildJoin(GuildCreateEvent event) async {
    try {
      final guild = event.guild;
      _logService.log('Bot joined guild: ${guild.name} (ID: ${guild.id})');

      // No automatic scanning or messages when joining a guild
    } catch (e, stackTrace) {
      _logService.error('Error processing guild join', e, stackTrace);
    }
  }

  bool get isInitialized => _isInitialized;
}




















package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	_ "github.com/lib/pq"
)

// BannedUser represents a user in the database
type BannedUser struct {
	DiscordUserID   string    `json:"discord_user_id"`
	DiscordUsername string    `json:"discord_username"`
	BannedAt        time.Time `json:"banned_at"`
}

var db *sql.DB

func main() {
	// Initialize database connection
	initDB()
	defer db.Close()

	// Create router
	r := mux.NewRouter()

	// Define routes
	r.HandleFunc("/api/users", getAllUsers).Methods("GET")
	r.<PERSON>("/api/users", addUser).Methods("POST")
	r.HandleFunc("/api/users/{id}", removeUser).Methods("DELETE")
	r.Handle<PERSON>unc("/api/users/check", checkUsers).Methods("POST")

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8081"
	}
	log.Printf("Starting server on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, r))
}

func initDB() {
	// Get database connection parameters from environment variables
	host := getEnv("DB_HOST", "localhost")
	port := getEnv("DB_PORT", "5432")
	dbname := getEnv("DB_NAME", "protect_bot")
	user := getEnv("DB_USER", "protect_bot_user")
	password := getEnv("DB_PASSWORD", "StrawberryYogurt")

	// Connect to database
	connStr := fmt.Sprintf("host=%s port=%s dbname=%s user=%s password=%s sslmode=disable",
		host, port, dbname, user, password)
	var err error
	db, err = sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Test connection
	err = db.Ping()
	if err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	// Create tables if they don't exist
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS logged_users (
			discord_user_id VARCHAR(255) PRIMARY KEY,
			discord_username VARCHAR(255) NOT NULL,
			banned_at TIMESTAMP NOT NULL,
			CONSTRAINT logged_users_user_id_key UNIQUE (discord_user_id)
		);
	`)
	if err != nil {
		log.Fatalf("Failed to create tables: %v", err)
	}

	log.Println("Connected to database successfully")
}

func getAllUsers(w http.ResponseWriter, r *http.Request) {
	// Query database for all users
	rows, err := db.Query("SELECT discord_user_id, discord_username, banned_at FROM logged_users ORDER BY banned_at DESC")
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to query database: %v", err), http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	// Parse results
	var users []BannedUser
	for rows.Next() {
		var user BannedUser
		err := rows.Scan(&user.DiscordUserID, &user.DiscordUsername, &user.BannedAt)
		if err != nil {
			http.Error(w, fmt.Sprintf("Failed to scan row: %v", err), http.StatusInternalServerError)
			return
		}
		users = append(users, user)
	}

	// Always return 200 OK with an array (empty if no users)
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if users == nil {
		w.Write([]byte("[]"))
		return
	}
	json.NewEncoder(w).Encode(users)
}

func addUser(w http.ResponseWriter, r *http.Request) {
	// Parse request body
	var user BannedUser
	err := json.NewDecoder(r.Body).Decode(&user)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to parse request body: %v", err), http.StatusBadRequest)
		return
	}

	// Validate user
	if user.DiscordUserID == "" {
		http.Error(w, "Discord user ID is required", http.StatusBadRequest)
		return
	}
	if user.DiscordUsername == "" {
		http.Error(w, "Discord username is required", http.StatusBadRequest)
		return
	}
	if user.BannedAt.IsZero() {
		user.BannedAt = time.Now()
	}

	// Insert user into database
	_, err = db.Exec(`
		INSERT INTO logged_users (discord_user_id, discord_username, banned_at)
		VALUES ($1, $2, $3)
		ON CONFLICT (discord_user_id) DO UPDATE SET
			discord_username = $2,
			banned_at = $3
	`, user.DiscordUserID, user.DiscordUsername, user.BannedAt)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to insert user: %v", err), http.StatusInternalServerError)
		return
	}

	// Return success
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]string{"message": "User added successfully"})
}

func removeUser(w http.ResponseWriter, r *http.Request) {
	// Get user ID from URL
	vars := mux.Vars(r)
	id := vars["id"]

	// Delete user from database
	result, err := db.Exec("DELETE FROM logged_users WHERE discord_user_id = $1", id)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to delete user: %v", err), http.StatusInternalServerError)
		return
	}

	// Check if user was deleted
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get rows affected: %v", err), http.StatusInternalServerError)
		return
	}
	if rowsAffected == 0 {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	// Return success
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "User removed successfully"})
}

// CheckUsersRequest represents a request to check users
type CheckUsersRequest struct {
	GuildMembers []GuildMember `json:"guild_members"`
}

// GuildMember represents a member in a guild
type GuildMember struct {
	ID       string `json:"id"`
	Username string `json:"username"`
}

// CheckUsersResponse represents a response from checking users
type CheckUsersResponse struct {
	MatchingUsers []BannedUser `json:"matching_users"`
}

func checkUsers(w http.ResponseWriter, r *http.Request) {
	// Parse request body
	var req CheckUsersRequest
	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to parse request body: %v", err), http.StatusBadRequest)
		return
	}

	log.Printf("Received %d guild members to check", len(req.GuildMembers))

	// Get all banned users (excluding empty IDs)
	rows, err := db.Query("SELECT discord_user_id, discord_username, banned_at FROM logged_users WHERE discord_user_id != '' AND discord_user_id IS NOT NULL")
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to query database: %v", err), http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	// Parse results
	var bannedUsers []BannedUser
	for rows.Next() {
		var user BannedUser
		err := rows.Scan(&user.DiscordUserID, &user.DiscordUsername, &user.BannedAt)
		if err != nil {
			http.Error(w, fmt.Sprintf("Failed to scan row: %v", err), http.StatusInternalServerError)
			return
		}
		bannedUsers = append(bannedUsers, user)
	}

	log.Printf("Found %d banned users in database", len(bannedUsers))
	for _, user := range bannedUsers {
		log.Printf("Banned user: ID=%s, Username=%s", user.DiscordUserID, user.DiscordUsername)
	}

	// Check for matches
	var matchingUsers []BannedUser
	for _, member := range req.GuildMembers {
		for _, bannedUser := range bannedUsers {
			log.Printf("Comparing member ID '%s' with banned ID '%s'", member.ID, bannedUser.DiscordUserID)
			if member.ID == bannedUser.DiscordUserID {
				log.Printf("MATCH FOUND: %s", member.ID)
				matchingUsers = append(matchingUsers, bannedUser)
				break
			}
		}
	}

	log.Printf("Found %d matching users", len(matchingUsers))

	// Always return a valid response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := CheckUsersResponse{
		MatchingUsers: matchingUsers,
	}

	if response.MatchingUsers == nil {
		response.MatchingUsers = []BannedUser{}
	}

	json.NewEncoder(w).Encode(response)
}

// Helper function to normalize IDs by removing non-digit characters
func normalizeID(id string) string {
	// Remove any non-numeric characters and return the ID
	return id
}

// Helper function to get environment variable with fallback
func getEnv(key, fallback string) string {
	value := os.Getenv(key)
	if value == "" {
		return fallback
	}
	return value
}
